<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ResNet50_EMA_PPM_Interactive Architecture</title>
    <style>
        body {
            font-family: 'Times New Roman', serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
            color: #333;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border: 2px solid #ddd;
            padding: 30px;
        }

        h1 {
            text-align: center;
            color: #000;
            margin-bottom: 30px;
            font-size: 1.8em;
            font-weight: bold;
        }

        .architecture-diagram {
            display: grid;
            grid-template-columns: 1fr 3fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }

        .input-section, .output-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
        }

        .main-pipeline {
            display: flex;
            flex-direction: column;
            gap: 15px;
            border: 2px dashed #666;
            padding: 20px;
            background: #fafafa;
        }

        .module-box {
            border: 2px solid #333;
            padding: 10px;
            text-align: center;
            background: white;
            font-size: 12px;
            font-weight: bold;
            min-height: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .input-box { background: #e8f4fd; border-color: #1976d2; }
        .conv-box { background: #fff3e0; border-color: #f57c00; }
        .resnet-box { background: #f3e5f5; border-color: #7b1fa2; }
        .ema-box { background: #e8f5e8; border-color: #388e3c; }
        .ppm-box { background: #fce4ec; border-color: #c2185b; }
        .fusion-box { background: #e3f2fd; border-color: #1565c0; }
        .output-box { background: #f1f8e9; border-color: #558b2f; }

        .parallel-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            border: 2px solid #666;
            padding: 15px;
            background: #f5f5f5;
        }

        .branch {
            display: flex;
            flex-direction: column;
            gap: 10px;
            border: 1px dashed #999;
            padding: 10px;
            background: white;
        }

        .branch-title {
            text-align: center;
            font-weight: bold;
            font-size: 14px;
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }

        .connection-line {
            height: 2px;
            background: #333;
            margin: 5px 0;
        }

        .arrow-down {
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-top: 12px solid #333;
            margin: 5px auto;
        }

        .shape-info {
            font-size: 10px;
            color: #666;
            font-style: italic;
        }

        .legend {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            background: #f9f9f9;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
        }

        .legend-color {
            width: 20px;
            height: 15px;
            border: 1px solid #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ResNet50_EMA_PPM_Interactive Architecture</h1>

        <div class="architecture-diagram">
            <!-- 输入部分 -->
            <div class="input-section">
                <div class="module-box input-box">
                    Input Image<br>
                    <span class="shape-info">(B, 3, 224, 224)</span>
                </div>
            </div>

            <!-- 主要流水线 -->
            <div class="main-pipeline">
                <!-- 初始卷积层 -->
                <div class="module-box conv-box">
                    Conv1 + BN + ReLU + MaxPool<br>
                    <span class="shape-info">7×7 conv, stride=2 → (B, 64, 56, 56)</span>
                </div>
                <div class="arrow-down"></div>

                <!-- ResNet Layer1 + EMA -->
                <div class="module-box resnet-box">
                    ResNet Layer1<br>
                    <span class="shape-info">(B, 256, 56, 56)</span>
                </div>
                <div class="module-box ema-box">
                    EMA Module (factor=8)<br>
                    <span class="shape-info">Efficient Multi-scale Attention</span>
                </div>
                <div class="arrow-down"></div>

                <!-- ResNet Layer2 + EMA -->
                <div class="module-box resnet-box">
                    ResNet Layer2<br>
                    <span class="shape-info">(B, 512, 28, 28)</span>
                </div>
                <div class="module-box ema-box">
                    EMA Module (factor=8)<br>
                    <span class="shape-info">Efficient Multi-scale Attention</span>
                </div>
                <div class="arrow-down"></div>

                <!-- 并行分支处理 -->
                <div class="parallel-section">
                    <!-- Layer3 分支 -->
                    <div class="branch">
                        <div class="branch-title">Layer3 Branch</div>
                        <div class="module-box resnet-box">
                            ResNet Layer3<br>
                            <span class="shape-info">(B, 1024, 14, 14)</span>
                        </div>
                        <div class="module-box ema-box">
                            EMA Module<br>
                            <span class="shape-info">factor=8</span>
                        </div>
                        <div class="module-box ppm-box">
                            Lightweight PPM<br>
                            <span class="shape-info">scales=(1,3,6)<br>→ (B, 256, 14, 14)</span>
                        </div>
                    </div>

                    <!-- Layer4 分支 -->
                    <div class="branch">
                        <div class="branch-title">Layer4 Branch</div>
                        <div class="module-box resnet-box">
                            ResNet Layer4<br>
                            <span class="shape-info">(B, 2048, 7, 7)</span>
                        </div>
                        <div class="module-box ema-box">
                            EMA Module<br>
                            <span class="shape-info">factor=8</span>
                        </div>
                        <div class="module-box ppm-box">
                            Full PPM<br>
                            <span class="shape-info">scales=(1,2,3,6)<br>→ (B, 512, 7, 7)</span>
                        </div>
                    </div>
                </div>

                <div class="arrow-down"></div>

                <!-- 跨尺度特征融合 -->
                <div class="module-box fusion-box">
                    Cross-Scale Fusion Module<br>
                    <span class="shape-info">Channel + Spatial Attention → (B, 512, 7, 7)</span>
                </div>
                <div class="arrow-down"></div>

                <!-- 全局池化和分类 -->
                <div class="module-box output-box">
                    Global Average Pooling<br>
                    <span class="shape-info">(B, 512, 7, 7) → (B, 512)</span>
                </div>
                <div class="module-box output-box">
                    Fully Connected Layer<br>
                    <span class="shape-info">(B, 512) → (B, num_classes)</span>
                </div>
            </div>

            <!-- 输出部分 -->
            <div class="output-section">
                <div class="module-box output-box">
                    Classification<br>
                    <span class="shape-info">Predictions</span>
                </div>
            </div>
        </div>

        <!-- 图例说明 -->
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color input-box"></div>
                <span>Input/Output</span>
            </div>
            <div class="legend-item">
                <div class="legend-color conv-box"></div>
                <span>Convolution</span>
            </div>
            <div class="legend-item">
                <div class="legend-color resnet-box"></div>
                <span>ResNet Block</span>
            </div>
            <div class="legend-item">
                <div class="legend-color ema-box"></div>
                <span>EMA Module</span>
            </div>
            <div class="legend-item">
                <div class="legend-color ppm-box"></div>
                <span>PPM Module</span>
            </div>
            <div class="legend-item">
                <div class="legend-color fusion-box"></div>
                <span>Fusion Module</span>
            </div>
        </div>

        <!-- 详细组件说明 -->
        <div style="margin-top: 30px; display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px;">
            <div style="border: 1px solid #ddd; padding: 15px; background: #f9f9f9;">
                <h3 style="margin-top: 0; color: #333; font-size: 14px;">EMA Module Details</h3>
                <ul style="font-size: 12px; margin: 0; padding-left: 15px;">
                    <li>Group processing (factor=8)</li>
                    <li>Horizontal & vertical pooling</li>
                    <li>1×1 and 3×3 convolutions</li>
                    <li>Attention weight generation</li>
                </ul>
            </div>

            <div style="border: 1px solid #ddd; padding: 15px; background: #f9f9f9;">
                <h3 style="margin-top: 0; color: #333; font-size: 14px;">PPM Module Details</h3>
                <ul style="font-size: 12px; margin: 0; padding-left: 15px;">
                    <li>Multi-scale pooling</li>
                    <li>Adaptive average pooling</li>
                    <li>Feature concatenation</li>
                    <li>Dimension reduction</li>
                </ul>
            </div>

            <div style="border: 1px solid #ddd; padding: 15px; background: #f9f9f9;">
                <h3 style="margin-top: 0; color: #333; font-size: 14px;">Cross-Scale Fusion</h3>
                <ul style="font-size: 12px; margin: 0; padding-left: 15px;">
                    <li>Channel alignment</li>
                    <li>Cross-scale attention</li>
                    <li>Spatial attention</li>
                    <li>Feature integration</li>
                </ul>
            </div>
        </div>

        <!-- 模型参数信息 -->
        <div style="margin-top: 20px; padding: 15px; border: 1px solid #ddd; background: #f0f8ff;">
            <h3 style="margin-top: 0; color: #333; font-size: 14px;">Model Configuration</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; font-size: 12px;">
                <div>
                    <strong>Architecture:</strong> ResNet50 + EMA + PPM<br>
                    <strong>Input Size:</strong> 224×224×3<br>
                    <strong>EMA Factor:</strong> 8<br>
                    <strong>PPM3 Scales:</strong> (1, 3, 6)
                </div>
                <div>
                    <strong>PPM4 Scales:</strong> (1, 2, 3, 6)<br>
                    <strong>Fusion Dim:</strong> 512<br>
                    <strong>Output Classes:</strong> 5 (DR grades)<br>
                    <strong>Drop Probability:</strong> 0.5
                </div>
            </div>
        </div>
    </div>
</body>
</html>
